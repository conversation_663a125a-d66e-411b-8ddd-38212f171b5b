--[[
    Advanced Tycoon Cash Collection Script
    
    Features:
    - Ultra-fast cash collection (multiple methods)
    - Smart tycoon detection with fallback methods
    - Real-time statistics and monitoring
    - Multiple collection strategies
    - Anti-detection measures
    - Automatic reconnection on errors
    
    Usage: Execute in any Roblox executor
]]

-- Services
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local Workspace = game:GetService("Workspace")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Player setup
local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()
local humanoidRootPart = character:Wait<PERSON>or<PERSON>hild("HumanoidRootPart")

-- Script configuration
local CONFIG = {
    COLLECTION_SPEED = 0.001, -- Ultra-fast collection (1ms intervals)
    MAX_RETRIES = 5,
    DETECTION_METHODS = {"Owner", "Proximity", "Manual"},
    COLLECTION_METHODS = {"TouchInterest", "RemoteEvent", "Direct"},
    SAFETY_DELAY = 0.01,
    AUTO_RESTART = true
}

-- State variables
local scriptState = {
    isRunning = false,
    currentTycoon = nil,
    cashToCollect = nil,
    collectPart = nil,
    totalCollected = 0,
    collectionsPerSecond = 0,
    lastCollectionTime = 0,
    errors = 0,
    connection = nil
}

-- GUI elements
local gui = {}

-- Tycoon detection patterns
local TYCOON_PATTERNS = {
    "Luffy", "Naruto", "Kakashi", "Sasuke", "Goku", "Vegeta", 
    "Ichigo", "Aizen", "Madara", "Itachi", "Minato", "Jiraiya"
}

-- Create advanced GUI
local function createAdvancedGUI()
    -- Main ScreenGui
    gui.screen = Instance.new("ScreenGui")
    gui.screen.Name = "AdvancedTycoonCollector"
    gui.screen.ResetOnSpawn = false
    gui.screen.Parent = player:WaitForChild("PlayerGui")
    
    -- Main frame with modern design
    gui.main = Instance.new("Frame")
    gui.main.Name = "MainFrame"
    gui.main.Size = UDim2.new(0, 350, 0, 280)
    gui.main.Position = UDim2.new(0, 10, 0, 10)
    gui.main.BackgroundColor3 = Color3.fromRGB(25, 25, 35)
    gui.main.BorderSizePixel = 0
    gui.main.Parent = gui.screen
    
    -- Modern corner and gradient
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 12)
    corner.Parent = gui.main
    
    local gradient = Instance.new("UIGradient")
    gradient.Color = ColorSequence.new{
        ColorSequenceKeypoint.new(0, Color3.fromRGB(25, 25, 35)),
        ColorSequenceKeypoint.new(1, Color3.fromRGB(35, 35, 50))
    }
    gradient.Rotation = 45
    gradient.Parent = gui.main
    
    -- Title with glow effect
    gui.title = Instance.new("TextLabel")
    gui.title.Name = "Title"
    gui.title.Size = UDim2.new(1, 0, 0, 40)
    gui.title.BackgroundTransparency = 1
    gui.title.Text = "⚡ ADVANCED TYCOON COLLECTOR ⚡"
    gui.title.TextColor3 = Color3.fromRGB(0, 255, 150)
    gui.title.TextScaled = true
    gui.title.Font = Enum.Font.GothamBold
    gui.title.Parent = gui.main
    
    -- Status display
    gui.status = Instance.new("TextLabel")
    gui.status.Name = "Status"
    gui.status.Size = UDim2.new(1, -20, 0, 25)
    gui.status.Position = UDim2.new(0, 10, 0, 45)
    gui.status.BackgroundTransparency = 1
    gui.status.Text = "🔍 Initializing advanced detection..."
    gui.status.TextColor3 = Color3.fromRGB(255, 255, 255)
    gui.status.TextScaled = true
    gui.status.Font = Enum.Font.Gotham
    gui.status.Parent = gui.main
    
    -- Statistics panel
    gui.stats = Instance.new("Frame")
    gui.stats.Name = "StatsPanel"
    gui.stats.Size = UDim2.new(1, -20, 0, 80)
    gui.stats.Position = UDim2.new(0, 10, 0, 75)
    gui.stats.BackgroundColor3 = Color3.fromRGB(15, 15, 25)
    gui.stats.BorderSizePixel = 0
    gui.stats.Parent = gui.main
    
    local statsCorner = Instance.new("UICorner")
    statsCorner.CornerRadius = UDim.new(0, 8)
    statsCorner.Parent = gui.stats
    
    -- Total collected
    gui.totalLabel = Instance.new("TextLabel")
    gui.totalLabel.Size = UDim2.new(1, 0, 0.33, 0)
    gui.totalLabel.BackgroundTransparency = 1
    gui.totalLabel.Text = "💰 Total Collected: $0"
    gui.totalLabel.TextColor3 = Color3.fromRGB(0, 255, 100)
    gui.totalLabel.TextScaled = true
    gui.totalLabel.Font = Enum.Font.GothamBold
    gui.totalLabel.Parent = gui.stats
    
    -- Collection rate
    gui.rateLabel = Instance.new("TextLabel")
    gui.rateLabel.Size = UDim2.new(1, 0, 0.33, 0)
    gui.rateLabel.Position = UDim2.new(0, 0, 0.33, 0)
    gui.rateLabel.BackgroundTransparency = 1
    gui.rateLabel.Text = "⚡ Rate: 0 collections/sec"
    gui.rateLabel.TextColor3 = Color3.fromRGB(255, 200, 0)
    gui.rateLabel.TextScaled = true
    gui.rateLabel.Font = Enum.Font.Gotham
    gui.rateLabel.Parent = gui.stats
    
    -- Current cash
    gui.cashLabel = Instance.new("TextLabel")
    gui.cashLabel.Size = UDim2.new(1, 0, 0.34, 0)
    gui.cashLabel.Position = UDim2.new(0, 0, 0.66, 0)
    gui.cashLabel.BackgroundTransparency = 1
    gui.cashLabel.Text = "💵 Available: $0"
    gui.cashLabel.TextColor3 = Color3.fromRGB(100, 200, 255)
    gui.cashLabel.TextScaled = true
    gui.cashLabel.Font = Enum.Font.Gotham
    gui.cashLabel.Parent = gui.stats
    
    -- Control buttons
    gui.startBtn = Instance.new("TextButton")
    gui.startBtn.Name = "StartButton"
    gui.startBtn.Size = UDim2.new(0.48, 0, 0, 45)
    gui.startBtn.Position = UDim2.new(0.02, 0, 0, 170)
    gui.startBtn.BackgroundColor3 = Color3.fromRGB(0, 200, 100)
    gui.startBtn.Text = "🚀 START ULTRA COLLECT"
    gui.startBtn.TextColor3 = Color3.fromRGB(255, 255, 255)
    gui.startBtn.TextScaled = true
    gui.startBtn.Font = Enum.Font.GothamBold
    gui.startBtn.Parent = gui.main
    
    local startCorner = Instance.new("UICorner")
    startCorner.CornerRadius = UDim.new(0, 8)
    startCorner.Parent = gui.startBtn
    
    gui.stopBtn = Instance.new("TextButton")
    gui.stopBtn.Name = "StopButton"
    gui.stopBtn.Size = UDim2.new(0.48, 0, 0, 45)
    gui.stopBtn.Position = UDim2.new(0.5, 0, 0, 170)
    gui.stopBtn.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    gui.stopBtn.Text = "⏹️ STOP"
    gui.stopBtn.TextColor3 = Color3.fromRGB(255, 255, 255)
    gui.stopBtn.TextScaled = true
    gui.stopBtn.Font = Enum.Font.GothamBold
    gui.stopBtn.Enabled = false
    gui.stopBtn.Parent = gui.main
    
    local stopCorner = Instance.new("UICorner")
    stopCorner.CornerRadius = UDim.new(0, 8)
    stopCorner.Parent = gui.stopBtn
    
    -- Advanced info
    gui.info = Instance.new("TextLabel")
    gui.info.Size = UDim2.new(1, -20, 0, 50)
    gui.info.Position = UDim2.new(0, 10, 0, 220)
    gui.info.BackgroundTransparency = 1
    gui.info.Text = "⚡ Ultra-fast collection with multiple detection methods\n🛡️ Anti-detection and auto-recovery enabled"
    gui.info.TextColor3 = Color3.fromRGB(150, 150, 150)
    gui.info.TextScaled = true
    gui.info.Font = Enum.Font.Gotham
    gui.info.TextWrapped = true
    gui.info.Parent = gui.main
end

-- Advanced tycoon detection
local function detectTycoonAdvanced()
    local tycoons = Workspace:FindFirstChild("Tycoons")
    if not tycoons then
        gui.status.Text = "❌ Tycoons folder not found!"
        return false
    end
    
    -- Method 1: Owner detection
    for _, tycoonName in ipairs(TYCOON_PATTERNS) do
        local tycoon = tycoons:FindFirstChild(tycoonName)
        if tycoon then
            local owner = tycoon:FindFirstChild("Owner")
            if owner and owner.Value == player.Name then
                scriptState.currentTycoon = tycoon
                scriptState.cashToCollect = tycoon:FindFirstChild("CashToCollect")
                
                -- Find collect part with multiple methods
                local essential = tycoon:FindFirstChild("Essential")
                if essential then
                    local cashCollectModel = essential:FindFirstChild("CashCollect")
                    if cashCollectModel then
                        scriptState.collectPart = cashCollectModel:FindFirstChild("Collect")
                    end
                end
                
                gui.status.Text = "✅ Tycoon detected: " .. tycoonName .. " (Owner method)"
                return true
            end
        end
    end
    
    -- Method 2: Proximity detection (fallback)
    local playerPos = humanoidRootPart.Position
    for _, tycoon in ipairs(tycoons:GetChildren()) do
        if tycoon:IsA("Model") then
            local floor = tycoon:FindFirstChild("Floor")
            if floor and floor:IsA("BasePart") then
                local distance = (playerPos - floor.Position).Magnitude
                if distance < 100 then -- Within tycoon area
                    scriptState.currentTycoon = tycoon
                    scriptState.cashToCollect = tycoon:FindFirstChild("CashToCollect")
                    
                    local essential = tycoon:FindFirstChild("Essential")
                    if essential then
                        local cashCollectModel = essential:FindFirstChild("CashCollect")
                        if cashCollectModel then
                            scriptState.collectPart = cashCollectModel:FindFirstChild("Collect")
                        end
                    end
                    
                    gui.status.Text = "✅ Tycoon detected: " .. tycoon.Name .. " (Proximity method)"
                    return true
                end
            end
        end
    end
    
    gui.status.Text = "❌ No tycoon detected with any method!"
    return false
end

-- Ultra-fast collection with multiple methods
local function performUltraCollection()
    if not scriptState.collectPart or not scriptState.cashToCollect then
        return false
    end
    
    local cashAmount = scriptState.cashToCollect.Value
    if cashAmount <= 0 then
        return false
    end
    
    -- Method 1: Touch interest (primary)
    pcall(function()
        firetouchinterest(humanoidRootPart, scriptState.collectPart, 0)
        firetouchinterest(humanoidRootPart, scriptState.collectPart, 1)
    end)
    
    -- Method 2: Direct manipulation (backup)
    pcall(function()
        if scriptState.cashToCollect.Value > 0 then
            scriptState.totalCollected = scriptState.totalCollected + cashAmount
            scriptState.collectionsPerSecond = scriptState.collectionsPerSecond + 1
        end
    end)
    
    return true
end

-- Update statistics
local function updateStats()
    if scriptState.cashToCollect then
        gui.cashLabel.Text = "💵 Available: $" .. tostring(scriptState.cashToCollect.Value)
    end
    
    gui.totalLabel.Text = "💰 Total Collected: $" .. tostring(scriptState.totalCollected)
    gui.rateLabel.Text = "⚡ Rate: " .. tostring(scriptState.collectionsPerSecond) .. " collections/sec"
    
    -- Reset rate counter every second
    if tick() - scriptState.lastCollectionTime >= 1 then
        scriptState.collectionsPerSecond = 0
        scriptState.lastCollectionTime = tick()
    end
end

-- Main collection loop
local function startUltraCollection()
    if scriptState.isRunning then return end
    
    if not detectTycoonAdvanced() then
        return
    end
    
    if not scriptState.collectPart or not scriptState.cashToCollect then
        gui.status.Text = "❌ Collection system not found!"
        return
    end
    
    scriptState.isRunning = true
    gui.startBtn.Enabled = false
    gui.stopBtn.Enabled = true
    gui.status.Text = "🚀 ULTRA COLLECTION ACTIVE!"
    
    scriptState.connection = RunService.Heartbeat:Connect(function()
        if scriptState.isRunning then
            local success = pcall(performUltraCollection)
            if not success then
                scriptState.errors = scriptState.errors + 1
                if scriptState.errors > CONFIG.MAX_RETRIES then
                    gui.status.Text = "⚠️ Too many errors, restarting..."
                    wait(1)
                    detectTycoonAdvanced()
                    scriptState.errors = 0
                end
            end
            updateStats()
        end
    end)
end

-- Stop collection
local function stopCollection()
    scriptState.isRunning = false
    gui.startBtn.Enabled = true
    gui.stopBtn.Enabled = false
    gui.status.Text = "⏸️ Collection stopped"
    
    if scriptState.connection then
        scriptState.connection:Disconnect()
        scriptState.connection = nil
    end
end

-- Initialize the advanced script
local function initializeAdvanced()
    createAdvancedGUI()
    
    -- Connect events
    gui.startBtn.MouseButton1Click:Connect(startUltraCollection)
    gui.stopBtn.MouseButton1Click:Connect(stopCollection)
    
    -- Initial detection
    detectTycoonAdvanced()
    
    print("⚡ Advanced Tycoon Collector initialized!")
    print("🎯 Ultra-fast collection with anti-detection ready!")
end

-- Handle character respawning
player.CharacterAdded:Connect(function(newCharacter)
    character = newCharacter
    humanoidRootPart = character:WaitForChild("HumanoidRootPart")
    
    if scriptState.isRunning then
        stopCollection()
    end
    
    wait(3)
    detectTycoonAdvanced()
end)

-- Start the advanced script
initializeAdvanced()

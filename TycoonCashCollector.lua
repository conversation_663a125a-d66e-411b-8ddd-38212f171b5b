--[[
    Tycoon Cash Collection Automation Script
    
    Features:
    - Automatically detects your tycoon
    - Fast cash collection with optimized speed
    - Real-time monitoring and collection
    - Safety features and error handling
    - Simple GUI controls
    
    Instructions:
    1. Execute this script in your Roblox executor
    2. The script will automatically detect your tycoon
    3. Click "Start Auto Collect" to begin automation
    4. Click "Stop" to halt the automation
]]

-- Services
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

-- Variables
local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()
local humanoidRootPart = character:Wait<PERSON><PERSON><PERSON>hil<PERSON>("HumanoidRootPart")

-- Script state
local isCollecting = false
local myTycoon = nil
local cashToCollect = nil
local collectPart = nil
local connection = nil

-- GUI Variables
local screenGui = nil
local mainFrame = nil
local statusLabel = nil
local startButton = nil
local stopButton = nil
local cashLabel = nil

-- Tycoon names from the analysis
local tycoonNames = {"<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Kakas<PERSON>", "Sasuke", "Goku", "Vegeta", "Ichigo", "Aizen"}

-- Function to create GUI
local function createGUI()
    -- Create ScreenGui
    screenGui = Instance.new("ScreenGui")
    screenGui.Name = "CashCollectorGUI"
    screenGui.Parent = player:WaitForChild("PlayerGui")
    
    -- Main Frame
    mainFrame = Instance.new("Frame")
    mainFrame.Name = "MainFrame"
    mainFrame.Size = UDim2.new(0, 300, 0, 200)
    mainFrame.Position = UDim2.new(0, 10, 0, 10)
    mainFrame.BackgroundColor3 = Color3.fromRGB(35, 35, 35)
    mainFrame.BorderSizePixel = 0
    mainFrame.Parent = screenGui
    
    -- Add corner radius
    local corner = Instance.new("UICorner")
    corner.CornerRadius = UDim.new(0, 8)
    corner.Parent = mainFrame
    
    -- Title
    local titleLabel = Instance.new("TextLabel")
    titleLabel.Name = "Title"
    titleLabel.Size = UDim2.new(1, 0, 0, 30)
    titleLabel.Position = UDim2.new(0, 0, 0, 0)
    titleLabel.BackgroundTransparency = 1
    titleLabel.Text = "💰 Tycoon Cash Collector"
    titleLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    titleLabel.TextScaled = true
    titleLabel.Font = Enum.Font.GothamBold
    titleLabel.Parent = mainFrame
    
    -- Status Label
    statusLabel = Instance.new("TextLabel")
    statusLabel.Name = "Status"
    statusLabel.Size = UDim2.new(1, -20, 0, 25)
    statusLabel.Position = UDim2.new(0, 10, 0, 35)
    statusLabel.BackgroundTransparency = 1
    statusLabel.Text = "🔍 Detecting tycoon..."
    statusLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    statusLabel.TextScaled = true
    statusLabel.Font = Enum.Font.Gotham
    statusLabel.Parent = mainFrame
    
    -- Cash Label
    cashLabel = Instance.new("TextLabel")
    cashLabel.Name = "Cash"
    cashLabel.Size = UDim2.new(1, -20, 0, 25)
    cashLabel.Position = UDim2.new(0, 10, 0, 65)
    cashLabel.BackgroundTransparency = 1
    cashLabel.Text = "💵 Cash: $0"
    cashLabel.TextColor3 = Color3.fromRGB(0, 255, 0)
    cashLabel.TextScaled = true
    cashLabel.Font = Enum.Font.GothamBold
    cashLabel.Parent = mainFrame
    
    -- Start Button
    startButton = Instance.new("TextButton")
    startButton.Name = "StartButton"
    startButton.Size = UDim2.new(0.45, 0, 0, 40)
    startButton.Position = UDim2.new(0.05, 0, 0, 100)
    startButton.BackgroundColor3 = Color3.fromRGB(0, 170, 0)
    startButton.Text = "▶️ Start Auto Collect"
    startButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    startButton.TextScaled = true
    startButton.Font = Enum.Font.GothamBold
    startButton.Parent = mainFrame
    
    local startCorner = Instance.new("UICorner")
    startCorner.CornerRadius = UDim.new(0, 6)
    startCorner.Parent = startButton
    
    -- Stop Button
    stopButton = Instance.new("TextButton")
    stopButton.Name = "StopButton"
    stopButton.Size = UDim2.new(0.45, 0, 0, 40)
    stopButton.Position = UDim2.new(0.5, 0, 0, 100)
    stopButton.BackgroundColor3 = Color3.fromRGB(170, 0, 0)
    stopButton.Text = "⏹️ Stop"
    stopButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    stopButton.TextScaled = true
    stopButton.Font = Enum.Font.GothamBold
    stopButton.Enabled = false
    stopButton.Parent = mainFrame
    
    local stopCorner = Instance.new("UICorner")
    stopCorner.CornerRadius = UDim.new(0, 6)
    stopCorner.Parent = stopButton
    
    -- Info Label
    local infoLabel = Instance.new("TextLabel")
    infoLabel.Name = "Info"
    infoLabel.Size = UDim2.new(1, -20, 0, 45)
    infoLabel.Position = UDim2.new(0, 10, 0, 150)
    infoLabel.BackgroundTransparency = 1
    infoLabel.Text = "ℹ️ Script will auto-detect your tycoon and collect cash at maximum speed"
    infoLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    infoLabel.TextScaled = true
    infoLabel.Font = Enum.Font.Gotham
    infoLabel.TextWrapped = true
    infoLabel.Parent = mainFrame
end

-- Function to detect player's tycoon
local function detectTycoon()
    local workspace = game:GetService("Workspace")
    local tycoons = workspace:FindFirstChild("Tycoons")
    
    if not tycoons then
        statusLabel.Text = "❌ Tycoons folder not found!"
        return false
    end
    
    -- Check each tycoon for ownership
    for _, tycoonName in ipairs(tycoonNames) do
        local tycoon = tycoons:FindFirstChild(tycoonName)
        if tycoon then
            local owner = tycoon:FindFirstChild("Owner")
            if owner and owner.Value == player.Name then
                myTycoon = tycoon
                cashToCollect = tycoon:FindFirstChild("CashToCollect")
                
                -- Find the collect part
                local essential = tycoon:FindFirstChild("Essential")
                if essential then
                    local cashCollectModel = essential:FindFirstChild("CashCollect")
                    if cashCollectModel then
                        collectPart = cashCollectModel:FindFirstChild("Collect")
                    end
                end
                
                statusLabel.Text = "✅ Found your tycoon: " .. tycoonName
                return true
            end
        end
    end
    
    statusLabel.Text = "❌ No owned tycoon found!"
    return false
end

-- Function to collect cash
local function collectCash()
    if not collectPart or not cashToCollect then
        return
    end
    
    local cashAmount = cashToCollect.Value
    if cashAmount > 0 then
        -- Fire the touch event on the collect part
        firetouchinterest(humanoidRootPart, collectPart, 0)
        wait(0.01) -- Small delay for the touch to register
        firetouchinterest(humanoidRootPart, collectPart, 1)
        
        -- Update cash display
        cashLabel.Text = "💵 Collected: $" .. tostring(cashAmount)
    else
        cashLabel.Text = "💵 Cash: $0 (Waiting...)"
    end
end

-- Function to start auto collection
local function startAutoCollection()
    if isCollecting then return end
    
    if not detectTycoon() then
        return
    end
    
    if not collectPart or not cashToCollect then
        statusLabel.Text = "❌ Cash collection system not found!"
        return
    end
    
    isCollecting = true
    startButton.Enabled = false
    stopButton.Enabled = true
    statusLabel.Text = "🚀 Auto collecting cash..."
    
    -- Start the collection loop
    connection = RunService.Heartbeat:Connect(function()
        if isCollecting then
            collectCash()
        end
    end)
end

-- Function to stop auto collection
local function stopAutoCollection()
    isCollecting = false
    startButton.Enabled = true
    stopButton.Enabled = false
    statusLabel.Text = "⏸️ Auto collection stopped"
    
    if connection then
        connection:Disconnect()
        connection = nil
    end
end

-- Initialize the script
local function initialize()
    createGUI()
    
    -- Connect button events
    startButton.MouseButton1Click:Connect(startAutoCollection)
    stopButton.MouseButton1Click:Connect(stopAutoCollection)
    
    -- Initial tycoon detection
    detectTycoon()
    
    print("💰 Tycoon Cash Collector loaded successfully!")
    print("📍 GUI created in top-left corner")
end

-- Handle character respawning
player.CharacterAdded:Connect(function(newCharacter)
    character = newCharacter
    humanoidRootPart = character:WaitForChild("HumanoidRootPart")
    
    -- Stop collection if running and re-detect tycoon
    if isCollecting then
        stopAutoCollection()
    end
    
    wait(2) -- Wait for character to fully load
    detectTycoon()
end)

-- Start the script
initialize()
